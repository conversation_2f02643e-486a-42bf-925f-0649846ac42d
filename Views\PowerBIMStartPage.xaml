﻿<Page x:Class="MEP.PowerBIM_6.Views.PowerBIMStartPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      mc:Ignorable="d" 
      Background="White"
      Title="PowerBIM Start">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

        </ResourceDictionary>

    </Page.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Project Settings Panel -->
        <GroupBox Grid.Row="0" Header="Project Settings" Margin="0,0,0,16">
            <Grid Margin="8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!-- Voltage Drop Settings -->
                <TextBlock Grid.Row="0" Grid.Column="0" Text="System VD Max:" VerticalAlignment="Center" Margin="0,0,8,0" />
                <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal">
                    <RadioButton x:Name="guiSysVD5pc" Content="5%" Margin="0,0,16,0" />
                    <RadioButton x:Name="guiSysVD7pc" Content="7%" />
                </StackPanel>

                <!-- Cable Selection -->
                <TextBlock Grid.Row="0" Grid.Column="2" Text="Cable Selection:" VerticalAlignment="Center" Margin="16,0,8,0" />
                <StackPanel Grid.Row="0" Grid.Column="3" Orientation="Horizontal">
                    <RadioButton x:Name="guiNZcableSel" Content="NZ (30°C)" Margin="0,0,16,0" />
                    <RadioButton x:Name="guiAUScableSel" Content="AUS (40°C)" />
                </StackPanel>
            </Grid>
        </GroupBox>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,0,0,16">
            <Button x:Name="btnRunSizer" Content="Run Auto Sizer" Margin="0,0,8,0" Padding="16,8" />
            <Button x:Name="btnSave" Content="Save" Margin="0,0,8,0" Padding="16,8" />
            <Button x:Name="btnExport" Content="Export" Margin="0,0,8,0" Padding="16,8" />
            <Button x:Name="btnHelp" Content="Help" Padding="16,8" />
        </StackPanel>

        <!-- Distribution Boards DataGrid -->
        <GroupBox Grid.Row="2" Header="Distribution Boards">
            <DataGrid 
                x:Name="dgvDBSel" 
                AutoGenerateColumns="False"
                CanUserAddRows="False"
                CanUserDeleteRows="False"
                GridLinesVisibility="All"
                HeadersVisibility="All"
                SelectionMode="Extended"
                Margin="8">

                <DataGrid.Columns>
                    <DataGridCheckBoxColumn Header="Select" Binding="{Binding IsSelected, Mode=TwoWay}" Width="60" />
                    <DataGridTextColumn Header="DB Name" Binding="{Binding Schedule_DB_Name}" Width="150" IsReadOnly="True" />
                    <DataGridTextColumn Header="Pass" Binding="{Binding Result_PassCount}" Width="60" IsReadOnly="True" />
                    <DataGridTextColumn Header="Warning" Binding="{Binding Result_WarningCount}" Width="80" IsReadOnly="True" />
                    <DataGridTextColumn Header="Fail" Binding="{Binding Result_FailCount}" Width="60" IsReadOnly="True" />
                    <DataGridTextColumn Header="Notes" Binding="{Binding GUI_Notes}" Width="200" IsReadOnly="True" />
                    <DataGridCheckBoxColumn Header="Update Required" Binding="{Binding Update_Required, Mode=OneWay}" Width="120" IsReadOnly="True" />
                </DataGrid.Columns>
            </DataGrid>
        </GroupBox>

        <!-- Status and Progress -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right">
            <TextBlock x:Name="StatusLabel" Text="Ready" VerticalAlignment="Center" Margin="0,0,16,0" />
            <ProgressBar x:Name="ProgressBar" Width="200" Height="20" Visibility="Collapsed" />
        </StackPanel>
    </Grid>
</Page>
