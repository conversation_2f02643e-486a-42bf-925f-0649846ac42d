﻿<Page
    x:Class="MEP.PowerBIM_6.Views.PowerBIMDistributionBoardsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.PowerBIM_6.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.PowerBIM_6.Views"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Distribution Boards"
    Background="White"
    mc:Ignorable="d">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <converters:BooleanToVisibilityConverter x:Key="BoolToVisConverter" />
            <converters:StatusColorConverter x:Key="StatusColorConverter" />
            <converters:NumericFormatConverter x:Key="NumericFormatConverter" />
            <converters:StringToVisibilityConverter x:Key="StringToVisibilityConverter" />
        </ResourceDictionary>

    </Page.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <StackPanel
            Grid.Row="0"
            Margin="0,0,0,16"
            Orientation="Horizontal">
            <materialDesign:PackIcon
                Width="24"
                Height="24"
                Margin="0,0,8,0"
                VerticalAlignment="Center"
                Kind="ViewDashboard" />
            <TextBlock
                VerticalAlignment="Center"
                Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                Text="Distribution Boards" />
        </StackPanel>

        <!--  Action Bar  -->
        <materialDesign:Card
            Grid.Row="1"
            Margin="0,0,0,16"
            Padding="16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  Main Action Buttons  -->
                <StackPanel
                    Grid.Row="0"
                    Margin="0,0,0,8"
                    Orientation="Horizontal">
                    <Button
                        Margin="0,0,8,0"
                        Command="{Binding LoadProjectDataCommand}"
                        Content="Load Project Data"
                        ToolTip="Load distribution boards from Revit project" />
                    <Button
                        Margin="0,0,8,0"
                        Command="{Binding RunAutoSizerCommand}"
                        Content="Run Auto Sizer"
                        ToolTip="Run automatic cable and breaker sizing for selected DBs" />
                    <Button
                        Margin="0,0,8,0"
                        Command="{Binding SaveCommand}"
                        Content="Save to Revit"
                        ToolTip="Save calculation results back to Revit" />
                    <Button
                        Margin="0,0,8,0"
                        Command="{Binding ExportCommand}"
                        Content="Export Results"
                        ToolTip="Export results to Excel or CSV" />
                    <Separator Margin="8,0" Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" />
                    <Button
                        Margin="0,0,8,0"
                        Content="Enhanced Circuit Edit"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        ToolTip="Open enhanced circuit editor" />
                    <Button
                        Margin="0,0,8,0"
                        Content="Write Lighting"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        ToolTip="Write lighting calculations to schedule" />
                    <Button
                        Margin="0,0,8,0"
                        Content="Write Power"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        ToolTip="Write power calculations to schedule" />
                </StackPanel>

                <!--  Selection and Filter Controls  -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <!--  Selection Controls  -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <Button
                            Margin="0,0,8,0"
                            Command="{Binding SelectAllDBsCommand}"
                            Content="Select All"
                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                        <Button
                            Margin="0,0,8,0"
                            Command="{Binding SelectNoneDBsCommand}"
                            Content="Select None"
                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                        <Button
                            Margin="0,0,16,0"
                            Command="{Binding RefreshCommand}"
                            Content="Refresh"
                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                    </StackPanel>

                    <!--  Selection Summary  -->
                    <StackPanel
                        Grid.Column="2"
                        VerticalAlignment="Center"
                        Orientation="Horizontal">
                        <TextBlock VerticalAlignment="Center" Text="Selected: " />
                        <TextBlock
                            Margin="0,0,8,0"
                            VerticalAlignment="Center"
                            FontWeight="SemiBold"
                            Text="{Binding SelectedDBCount}" />
                        <TextBlock
                            Margin="0,0,4,0"
                            VerticalAlignment="Center"
                            Text="of" />
                        <TextBlock
                            VerticalAlignment="Center"
                            FontWeight="SemiBold"
                            Text="{Binding DistributionBoards.Count}" />
                        <TextBlock
                            Margin="4,0,0,0"
                            VerticalAlignment="Center"
                            Text="DBs" />
                    </StackPanel>
                </Grid>
            </Grid>
        </materialDesign:Card>

        <!-- Distribution Boards DataGrid -->
        <materialDesign:Card Grid.Row="2" Padding="8">
            <DataGrid x:Name="dgDistributionBoards"
                      ItemsSource="{Binding DistributionBoards}"
                      
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      SelectionMode="Extended"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      AlternatingRowBackground="{DynamicResource MaterialDesignDivider}"
                      RowBackground="Transparent">

                <DataGrid.Columns>
                    <!-- Manual Lock Column (matches original ManualLock column) -->
                    <DataGridCheckBoxColumn Header="Lock"
                                            Binding="{Binding IsManuallyLocked}"
                                            Width="50"
                                     
                                            ElementStyle="{StaticResource MaterialDesignDataGridCheckBoxColumnStyle}" />

                    <!-- DB Name Column (matches original dBNameDataGridViewTextBoxColumn) -->
                    <DataGridTextColumn Header="DB Name"
                                        Binding="{Binding DisplayName}"
                                        Width="180"
                                        IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                                <Setter Property="FontWeight" Value="SemiBold" />
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsLocked}" Value="True">
                                        <Setter Property="Foreground" Value="Gray" />
                                        <Setter Property="FontStyle" Value="Italic" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Result_FailCount}" Value="0">
                                        <Setter Property="Foreground" Value="Green" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Data_Good}" Value="False">
                                        <Setter Property="Foreground" Value="LightGray" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- Pass Count Column (matches original dBPassCountDataGridViewTextBoxColumn) -->
                    <DataGridTextColumn Header="Pass"
                                        Binding="{Binding Result_PassCount}"
                                        Width="60"
                                        IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                                <Setter Property="Foreground" Value="Green" />
                                <Setter Property="FontWeight" Value="SemiBold" />
                                <Setter Property="HorizontalAlignment" Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- Warning Count Column (matches original dBWarningCountDataGridViewTextBoxColumn) -->
                    <DataGridTextColumn Header="Warning"
                                        Binding="{Binding Result_WarningCount}"
                                        Width="80"
                                        IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                                <Setter Property="Foreground" Value="Orange" />
                                <Setter Property="FontWeight" Value="SemiBold" />
                                <Setter Property="HorizontalAlignment" Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- Fail Count Column (matches original dBFailCountDataGridViewTextBoxColumn) -->
                    <DataGridTextColumn Header="Fail"
                                        Binding="{Binding Result_FailCount}"
                                        Width="60"
                                        IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignDataGridTextColumnStyle}">
                                <Setter Property="Foreground" Value="Red" />
                                <Setter Property="FontWeight" Value="SemiBold" />
                                <Setter Property="HorizontalAlignment" Value="Center" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- User Notes Button Column (matches original DB_UserNotes) -->
                    <DataGridTemplateColumn Header="User Notes" Width="100">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Button Content="{Binding User_Notes, TargetNullValue='Add notes', FallbackValue='Add notes'}"
                                        Style="{StaticResource MaterialDesignFlatButton}"
                                        FontSize="10"
                                        Padding="4,2"
                                        ToolTip="Click to add or edit user notes"
                                        Command="{Binding DataContext.EditUserNotesCommand, RelativeSource={RelativeSource AncestorType=Page}}"
                                        CommandParameter="{Binding}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- GUI Notes Column (matches original dBNotesDataGridViewTextBoxColumn) -->
                    <DataGridTemplateColumn Header="System Notes" Width="200">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Button Content="View Details"
                                        Style="{StaticResource MaterialDesignFlatButton}"
                                        FontSize="10"
                                        Padding="4,2"
                                        ToolTip="{Binding GUI_Notes}"
                                        Command="{Binding DataContext.ViewSystemNotesCommand, RelativeSource={RelativeSource AncestorType=Page}}"
                                        CommandParameter="{Binding}"
                                        Visibility="{Binding GUI_Notes, Converter={StaticResource StringToVisibilityConverter}}" />
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- DB Settings Button Column (matches original DB_Settings) -->
                    <DataGridTemplateColumn Header="Settings" Width="80">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                        ToolTip="Edit DB Settings"
                                        Command="{Binding DataContext.EditDBSettingsCommand, RelativeSource={RelativeSource AncestorType=Page}}"
                                        CommandParameter="{Binding}">
                                    <materialDesign:PackIcon Kind="Settings" Width="16" Height="16" />
                                </Button>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>

                    <!-- Update Required Column (matches original DB_UpdateRequired) -->
                    <DataGridCheckBoxColumn Header="Update Required"
                                            Binding="{Binding Update_Required}"
                                            Width="120"
                                            IsReadOnly="True"
                                            ElementStyle="{StaticResource MaterialDesignDataGridCheckBoxColumnStyle}" />
                </DataGrid.Columns>

                <!-- Row Style for highlighting -->
                <DataGrid.RowStyle>
                    <Style TargetType="DataGridRow" BasedOn="{StaticResource MaterialDesignDataGridRow}">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                <Setter Property="Background" Value="{DynamicResource PrimaryHueLightBrush}" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding Result_FailCount}" Value="0">
                                <DataTrigger.Setters>
                                    <Setter Property="BorderBrush" Value="Green" />
                                    <Setter Property="BorderThickness" Value="0,0,0,1" />
                                </DataTrigger.Setters>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>
            </DataGrid>
        </materialDesign:Card>


    </Grid>
</Page>
