﻿using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using CommunityToolkit.Mvvm.Input;
using MEP.PowerBIM_6.Models;
using MEP.PowerBIM_6.Services;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;

namespace MEP.PowerBIM_6.ViewModels
{
    /// <summary>
    /// Enhanced Main ViewModel for PowerBIM application with full MVVM support
    /// </summary>
    public class PowerBIMMainViewModelEnhanced : BaseViewModel
    {
        #region Private Fields

        private PowerBIMProjectInfoModel _projectInfo;
        private ObservableCollection<PowerBIMDBDataModelEnhanced> _distributionBoards;
        private ObservableCollection<PowerBIMDBDataModelEnhanced> _selectedDistributionBoards;
        private bool _isInitialized;
        private double _progressValue;
        private bool _isProgressVisible;

        // Services
        private readonly PowerBIMCalculationService _calculationService;
        private readonly ExternalEvent _externalEvent;
        private readonly RequestHandler _requestHandler;
        private readonly BecaActivityLoggerData _logger;
        private readonly UIApplication _uiApplication;
        private readonly PowerBIMValidationService _validationService;
        private readonly PowerBIMPerformanceMonitor _performanceMonitor;
        private readonly PowerBIMExportService _exportService;

        #endregion

        #region Constructor

        public PowerBIMMainViewModelEnhanced(
            ExternalEvent externalEvent,
            RequestHandler requestHandler,
            BecaActivityLoggerData logger,
            UIApplication uiApplication)
        {
            _externalEvent = externalEvent;
            _requestHandler = requestHandler;
            _logger = logger;
            _uiApplication = uiApplication;
            _calculationService = new PowerBIMCalculationService(uiApplication, logger);
            _validationService = new PowerBIMValidationService();
            _performanceMonitor = PowerBIMPerformanceMonitor.Instance;
            _exportService = new PowerBIMExportService();

            _projectInfo = new PowerBIMProjectInfoModel
            {
                UIDocument = uiApplication.ActiveUIDocument,
                Document = uiApplication.ActiveUIDocument?.Document,
                TaskLogger = logger
            };

            _distributionBoards = new ObservableCollection<PowerBIMDBDataModelEnhanced>();
            _selectedDistributionBoards = new ObservableCollection<PowerBIMDBDataModelEnhanced>();

            InitializeCommands();
            SetupCollectionHandlers();
        }

        #endregion

        #region Properties

        /// <summary>
        /// Gets the project information model
        /// </summary>
        public PowerBIMProjectInfoModel ProjectInfo
        {
            get => _projectInfo;
            set => SetProperty(ref _projectInfo, value);
        }

        /// <summary>
        /// Gets the collection of all distribution boards in the project
        /// </summary>
        public ObservableCollection<PowerBIMDBDataModelEnhanced> DistributionBoards
        {
            get => _distributionBoards;
            set => SetProperty(ref _distributionBoards, value ?? new ObservableCollection<PowerBIMDBDataModelEnhanced>());
        }

        /// <summary>
        /// Gets the collection of selected distribution boards
        /// </summary>
        public ObservableCollection<PowerBIMDBDataModelEnhanced> SelectedDistributionBoards
        {
            get => _selectedDistributionBoards;
            set => SetProperty(ref _selectedDistributionBoards, value ?? new ObservableCollection<PowerBIMDBDataModelEnhanced>());
        }

        /// <summary>
        /// Gets or sets whether the application is initialized
        /// </summary>
        public bool IsInitialized
        {
            get => _isInitialized;
            set => SetProperty(ref _isInitialized, value);
        }

        /// <summary>
        /// Gets or sets the progress value (0-100)
        /// </summary>
        public double ProgressValue
        {
            get => _progressValue;
            set => SetProperty(ref _progressValue, value);
        }

        /// <summary>
        /// Gets or sets whether the progress bar is visible
        /// </summary>
        public bool IsProgressVisible
        {
            get => _isProgressVisible;
            set => SetProperty(ref _isProgressVisible, value);
        }

        /// <summary>
        /// Gets the number of selected distribution boards
        /// </summary>
        public int SelectedDBCount
        {
            get
            {
                try
                {
                    return DistributionBoards?.Count(db => db?.IsSelected == true) ?? 0;
                }
                catch
                {
                    return 0;
                }
            }
        }

        /// <summary>
        /// Gets whether any distribution boards are selected
        /// </summary>
        public bool HasSelectedDBs => SelectedDBCount > 0;

        /// <summary>
        /// Gets the total number of circuits across all DBs
        /// </summary>
        public int TotalCircuitCount
        {
            get
            {
                try
                {
                    return DistributionBoards?.Sum(db => db?.TotalCircuitCount ?? 0) ?? 0;
                }
                catch
                {
                    return 0;
                }
            }
        }

        /// <summary>
        /// Gets the total number of passing circuits
        /// </summary>
        public int TotalPassCount
        {
            get
            {
                try
                {
                    return DistributionBoards?.Sum(db => db?.Result_PassCount ?? 0) ?? 0;
                }
                catch
                {
                    return 0;
                }
            }
        }

        /// <summary>
        /// Gets the total number of warning circuits
        /// </summary>
        public int TotalWarningCount
        {
            get
            {
                try
                {
                    return DistributionBoards?.Sum(db => db?.Result_WarningCount ?? 0) ?? 0;
                }
                catch
                {
                    return 0;
                }
            }
        }

        /// <summary>
        /// Gets the total number of failing circuits
        /// </summary>
        public int TotalFailCount
        {
            get
            {
                try
                {
                    return DistributionBoards?.Sum(db => db?.Result_FailCount ?? 0) ?? 0;
                }
                catch
                {
                    return 0;
                }
            }
        }

        #endregion

        #region Project Settings Properties (Delegated to ProjectInfo)

        /// <summary>
        /// Gets or sets whether system VD is 5%
        /// </summary>
        public bool SystemVD5Percent
        {
            get => ProjectInfo?.IsSystemVD5Percent ?? true;
            set
            {
                if (ProjectInfo != null)
                    ProjectInfo.IsSystemVD5Percent = value;
            }
        }

        /// <summary>
        /// Gets or sets whether system VD is 7%
        /// </summary>
        public bool SystemVD7Percent
        {
            get => ProjectInfo?.IsSystemVD7Percent ?? false;
            set
            {
                if (ProjectInfo != null)
                    ProjectInfo.IsSystemVD7Percent = value;
            }
        }

        /// <summary>
        /// Gets or sets whether NZ cable selection is active (30°C)
        /// </summary>
        public bool NZCableSelection
        {
            get => ProjectInfo?.IsNZCableSelection ?? true;
            set
            {
                if (ProjectInfo != null)
                    ProjectInfo.IsNZCableSelection = value;
            }
        }

        /// <summary>
        /// Gets or sets whether AUS cable selection is active (40°C)
        /// </summary>
        public bool AUSCableSelection
        {
            get => ProjectInfo?.IsAUSCableSelection ?? false;
            set
            {
                if (ProjectInfo != null)
                    ProjectInfo.IsAUSCableSelection = value;
            }
        }

        #endregion

        #region Commands

        public ICommand RunAutoSizerCommand { get; private set; }
        public ICommand SaveCommand { get; private set; }
        public ICommand ExportCommand { get; private set; }
        public ICommand HelpCommand { get; private set; }
        public ICommand LoadProjectDataCommand { get; private set; }
        public ICommand SelectAllDBsCommand { get; private set; }
        public ICommand SelectNoneDBsCommand { get; private set; }
        public ICommand RefreshCommand { get; private set; }
        public ICommand EditUserNotesCommand { get; private set; }
        public ICommand ViewSystemNotesCommand { get; private set; }
        public ICommand EditDBSettingsCommand { get; private set; }
        public ICommand EnhancedCircuitEditCommand { get; private set; }
        public ICommand WriteLightingCommand { get; private set; }
        public ICommand WritePowerCommand { get; private set; }
        public ICommand AdvancedSettingsCommand { get; private set; }

        private void InitializeCommands()
        {
            //RunAutoSizerCommand = new AsyncRelayCommand(RunAutoSizerAsync, () => HasSelectedDBs && !IsBusy);
            //SaveCommand = new AsyncRelayCommand(SaveAsync, () => IsInitialized && !IsBusy);
            //ExportCommand = new RelayCommand(Export, () => IsInitialized && !IsBusy);
            //HelpCommand = new RelayCommand(ShowHelp);
            //LoadProjectDataCommand = new AsyncRelayCommand(LoadProjectDataAsync, () => !IsBusy);
            //SelectAllDBsCommand = new RelayCommand(SelectAllDBs, () => DistributionBoards?.Any() == true);
            //SelectNoneDBsCommand = new RelayCommand(SelectNoneDBs, () => DistributionBoards?.Any() == true);
            //RefreshCommand = new AsyncRelayCommand(RefreshDataAsync, () => !IsBusy);
            //EditUserNotesCommand = new RelayCommand<PowerBIMDBDataModelEnhanced>(EditUserNotes);
            //ViewSystemNotesCommand = new RelayCommand<PowerBIMDBDataModelEnhanced>(ViewSystemNotes);
            //EditDBSettingsCommand = new RelayCommand<PowerBIMDBDataModelEnhanced>(EditDBSettings);
            //EnhancedCircuitEditCommand = new RelayCommand(OpenEnhancedCircuitEdit, () => HasSelectedDBs);
            //WriteLightingCommand = new AsyncRelayCommand(WriteLightingAsync, () => HasSelectedDBs && !IsBusy);
            //WritePowerCommand = new AsyncRelayCommand(WritePowerAsync, () => HasSelectedDBs && !IsBusy);
            //HelpCommand = new RelayCommand(ShowHelp);
            //AdvancedSettingsCommand = new RelayCommand(ShowAdvancedSettings, () => !IsBusy);
        }

        #endregion

        #region Command Implementations

        private async Task RunAutoSizerAsync()
        {
            SetBusyState(true, "Running auto sizer...");
            IsProgressVisible = true;
            ProgressValue = 0;

            try
            {
                var selectedDBs = DistributionBoards.Where(db => db.IsSelected).ToList();
                if (!selectedDBs.Any())
                {
                    StatusMessage = "No distribution boards selected";
                    return;
                }

                // Validate data before running auto sizer
                StatusMessage = "Validating data...";
                ProgressValue = 10;

                var validationResult = _validationService.ValidateComplete(ProjectInfo, DistributionBoards);

                if (!validationResult.IsValid)
                {
                    StatusMessage = $"Validation failed: {validationResult.Errors.Count} errors found";
                    _logger?.PostTaskEnd($"Auto sizer validation failed: {string.Join(", ", validationResult.Errors)}");
                    return;
                }

                if (validationResult.HasWarnings)
                {
                    StatusMessage = $"Validation warnings: {validationResult.Warnings.Count} warnings found";
                    _logger?.PostTaskEnd($"Auto sizer validation warnings: {string.Join(", ", validationResult.Warnings)}");
                }

                ProgressValue = 20;

                // Run the auto sizer with performance monitoring
                var progress = new Progress<string>(message =>
                {
                    StatusMessage = message;
                    ProgressValue = Math.Min(ProgressValue + 10, 90);
                });

                var success = await _performanceMonitor.MeasureAsync("AutoSizer", async () =>
                {
                    return await _calculationService.RunAutoSizerAsync(ProjectInfo, DistributionBoards, progress);
                });

                ProgressValue = 100;

                if (success)
                {
                    StatusMessage = $"Auto sizer completed successfully for {selectedDBs.Count} distribution boards";
                    UpdateSummaryCounts();

                    // Print performance report to debug output
                    _performanceMonitor.PrintReport();
                }
                else
                {
                    StatusMessage = "Auto sizer completed with errors";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Auto sizer error: {ex.Message}";
                _logger?.PostTaskEnd($"Auto sizer error: {ex.Message}");
            }
            finally
            {
                SetBusyState(false);
                IsProgressVisible = false;
                ProgressValue = 0;
            }
        }

        private async Task SaveAsync()
        {
            SetBusyState(true, "Saving...");
            try
            {
                // TODO: Implement save logic using RequestHandler
                Request.Make(RequestId.SavePowerBIM);
                _externalEvent.Raise();

                await Task.Delay(500); // Wait for external event
                StatusMessage = "Saved successfully";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Save error: {ex.Message}";
                _logger?.PostTaskEnd($"Save error: {ex.Message}");
            }
            finally
            {
                SetBusyState(false);
            }
        }

        private async void Export(object parameter)
        {
            SetBusyState(true, "Exporting data...");
            try
            {
                // Check if there's data to export
                if (DistributionBoards == null || !DistributionBoards.Any(db => db.IsSelected))
                {
                    StatusMessage = "No distribution boards selected for export";
                    return;
                }

                // Show export dialog and perform export
                var success = await _exportService.ShowExportDialogAsync(ProjectInfo, DistributionBoards);

                StatusMessage = success ? "Export completed successfully" : "Export cancelled or failed";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Export error: {ex.Message}";
                _logger?.PostTaskEnd($"Export error: {ex.Message}");
            }
            finally
            {
                SetBusyState(false);
            }
        }

        private void ShowHelp(object parameter)
        {
            // TODO: Implement help dialog
            StatusMessage = "Help functionality not yet implemented";
        }

        private async Task LoadProjectDataAsync()
        {
            SetBusyState(true, "Loading project data...");
            IsProgressVisible = true;
            ProgressValue = 0;

            try
            {
                var progress = new Progress<string>(message =>
                {
                    StatusMessage = message;
                    ProgressValue = Math.Min(ProgressValue + 20, 90);
                });

                var success = await _calculationService.LoadProjectDataAsync(ProjectInfo, DistributionBoards, progress);

                ProgressValue = 100;
                StatusMessage = success ? "Project data loaded successfully" : "Failed to load project data";
                IsInitialized = success;

                // Update summary counts
                UpdateSummaryCounts();
            }
            catch (Exception ex)
            {
                StatusMessage = $"Load error: {ex.Message}";
                _logger?.PostTaskEnd($"Load error: {ex.Message}");
            }
            finally
            {
                SetBusyState(false);
                IsProgressVisible = false;
                ProgressValue = 0;
            }
        }

        private void SelectAllDBs(object parameter)
        {
            foreach (var db in DistributionBoards)
                db.IsSelected = true;
            UpdateSelectionCounts();
        }

        private void SelectNoneDBs(object parameter)
        {
            foreach (var db in DistributionBoards)
                db.IsSelected = false;
            UpdateSelectionCounts();
        }

        private async Task RefreshDataAsync()
        {
            await LoadProjectDataAsync();
        }

        private void EditUserNotes(PowerBIMDBDataModelEnhanced db)
        {
            //if (db == null) return;

            //try
            //{
            //    // Show the user notes dialog
            //    var result = Views.PowerBIMUserNotesDialog.ShowDialog(
            //        System.Windows.Application.Current.MainWindow,
            //        db);

            //    if (result)
            //    {
            //        StatusMessage = $"User notes updated for {db.Schedule_DB_Name}";

            //        // Trigger property change notifications
            //        OnPropertyChanged(nameof(DistributionBoards));
            //    }
            //    else
            //    {
            //        StatusMessage = $"User notes edit cancelled for {db.Schedule_DB_Name}";
            //    }
            //}
            //catch (Exception ex)
            //{
            //    StatusMessage = $"Error editing user notes: {ex.Message}";
            //    _logger?.PostTaskEnd($"Error editing user notes: {ex.Message}");
            //}
        }

        private void ViewSystemNotes(PowerBIMDBDataModelEnhanced db)
        {
            if (db == null) return;

            try
            {
                // TODO: Implement system notes viewer
                // For now, show the notes in the status
                if (!string.IsNullOrEmpty(db.GUI_Notes))
                {
                    StatusMessage = $"System Notes for {db.Schedule_DB_Name}: {db.GUI_Notes}";
                }
                else
                {
                    StatusMessage = $"No system notes for {db.Schedule_DB_Name}";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error viewing system notes: {ex.Message}";
                _logger?.PostTaskEnd($"Error viewing system notes: {ex.Message}");
            }
        }

        private void EditDBSettings(PowerBIMDBDataModelEnhanced db)
        {
            //if (db == null) return;

            //try
            //{
            //    // Show the DB edit dialog
            //    var result = Views.PowerBIMDBEditDialog.ShowDialog(
            //        System.Windows.Application.Current.MainWindow,
            //        db);

            //    if (result)
            //    {
            //        StatusMessage = $"Settings updated for {db.Schedule_DB_Name}";

            //        // Trigger property change notifications
            //        OnPropertyChanged(nameof(DistributionBoards));

            //        // If calculation was performed, update the UI
            //        if (db.Update_Required)
            //        {
            //            UpdateSummaryStatistics();
            //        }
            //    }
            //    else
            //    {
            //        StatusMessage = $"Settings edit cancelled for {db.Schedule_DB_Name}";
            //    }
            //}
            //catch (Exception ex)
            //{
            //    StatusMessage = $"Error editing DB settings: {ex.Message}";
            //    _logger?.PostTaskEnd($"Error editing DB settings: {ex.Message}");
            //}
        }

        private void OpenEnhancedCircuitEdit(object parameter)
        {
            //try
            //{
            //    // Navigate to the circuit edit page
            //    // This requires access to the navigation service from the main window
            //    StatusMessage = "Opening Enhanced Circuit Editor...";

            //    // Send a message to navigate to the circuit edit page
            //    WeakReferenceMessenger.Default.Send(new NavigationMessage("CircuitEdit"));

            //    StatusMessage = "Enhanced Circuit Editor opened";
            //}
            //catch (Exception ex)
            //{
            //    StatusMessage = $"Error opening circuit editor: {ex.Message}";
            //    _logger?.PostTaskEnd($"Error opening circuit editor: {ex.Message}");
            //}
        }

        private async Task WriteLightingAsync()
        {
            SetBusyState(true, "Writing lighting calculations...");
            try
            {
                // TODO: Implement write lighting logic using RequestHandler
                Request.Make(RequestId.WriteLightingToSchedule);
                _externalEvent.Raise();

                await Task.Delay(1000); // Wait for external event
                StatusMessage = "Lighting calculations written to schedule";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Write lighting error: {ex.Message}";
                _logger?.PostTaskEnd($"Write lighting error: {ex.Message}");
            }
            finally
            {
                SetBusyState(false);
            }
        }

        private async Task WritePowerAsync()
        {
            SetBusyState(true, "Writing power calculations...");
            try
            {
                // TODO: Implement write power logic using RequestHandler
                Request.Make(RequestId.WritePowerToSchedule);
                _externalEvent.Raise();

                await Task.Delay(1000); // Wait for external event
                StatusMessage = "Power calculations written to schedule";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Write power error: {ex.Message}";
                _logger?.PostTaskEnd($"Write power error: {ex.Message}");
            }
            finally
            {
                SetBusyState(false);
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Initialize the ViewModel with project data
        /// </summary>
        public async Task InitializeAsync()
        {
            if (!IsInitialized)
            {
                // Set static references for RequestHandler
                Services.RequestHandler.CurrentProjectInfo = ProjectInfo;
                Services.RequestHandler.CurrentDistributionBoards = DistributionBoards;

                await LoadProjectDataAsync();
            }
        }

        /// <summary>
        /// Called when the main window is closing
        /// </summary>
        public override void OnViewClosing()
        {
            // Cleanup logic
            _logger?.PostTaskEnd("PowerBIM WPF window closed");
        }

        #endregion

        #region Private Methods

        private void SetupCollectionHandlers()
        {
            // Monitor changes to distribution board selection
            DistributionBoards.CollectionChanged += (s, e) =>
            {
                UpdateSelectionCounts();

                // Subscribe to property changes for new items
                if (e.NewItems != null)
                {
                    foreach (PowerBIMDBDataModelEnhanced db in e.NewItems)
                    {
                        db.PropertyChanged += (sender, args) =>
                        {
                            if (args.PropertyName == nameof(PowerBIMDBDataModelEnhanced.IsSelected))
                                UpdateSelectionCounts();
                        };
                    }
                }
            };
        }

        private void UpdateSelectionCounts()
        {
            OnPropertyChanged(nameof(SelectedDBCount));
            OnPropertyChanged(nameof(HasSelectedDBs));
        }

        private void UpdateSummaryCounts()
        {
            OnPropertyChanged(nameof(TotalCircuitCount));
            OnPropertyChanged(nameof(TotalPassCount));
            OnPropertyChanged(nameof(TotalWarningCount));
            OnPropertyChanged(nameof(TotalFailCount));
        }

        //private void ShowHelp(object parameter)
        //{
        //    try
        //    {
        //        // Show the help dialog
        //        Views.PowerBIMHelpDialog.ShowDialog(System.Windows.Application.Current.MainWindow);
        //        StatusMessage = "Help dialog opened";
        //    }
        //    catch (Exception ex)
        //    {
        //        StatusMessage = $"Error opening help: {ex.Message}";
        //        _logger?.PostTaskEnd($"Error opening help: {ex.Message}");
        //    }
        //}

        private void ShowAdvancedSettings(object parameter)
        {
            //try
            //{
            //    // Show the advanced settings dialog
            //    var result = Views.PowerBIMAdvancedSettingsDialog.ShowDialog(
            //        System.Windows.Application.Current.MainWindow,
            //        ProjectInfo);

            //    if (result)
            //    {
            //        StatusMessage = "Advanced settings updated";

            //        // Trigger property change notifications
            //        OnPropertyChanged(nameof(ProjectInfo));
            //    }
            //    else
            //    {
            //        StatusMessage = "Advanced settings cancelled";
            //    }
            //}
            //catch (Exception ex)
            //{
            //    StatusMessage = $"Error opening advanced settings: {ex.Message}";
            //    _logger?.PostTaskEnd($"Error opening advanced settings: {ex.Message}");
            //}
        }

        #endregion
    }
}
