﻿using CommunityToolkit.Mvvm.Messaging;
using MEP.PowerBIM_6.Services;
using MEP.PowerBIM_6.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Frame = System.Windows.Controls.Frame;

namespace MEP.PowerBIM_6.Views
{
    /// <summary>
    /// Interaction logic for MainWindowEnhanced.xaml
    /// </summary>
    public partial class MainWindowEnhanced : Window
    {
        #region Private Fields

        private readonly NavigationService _navigationService;
        private PowerBIMMainViewModelEnhanced _viewModel;

        #endregion

        public MainWindowEnhanced(PowerBIMMainViewModelEnhanced viewModel)
        {
            InitializeComponent();

            _viewModel = viewModel;
            DataContext = _viewModel;

            _navigationService = new NavigationService(MainFrame);

            // Subscribe to navigation messages
            WeakReferenceMessenger.Default.Register<Services.NavigationMessage>(this, HandleNavigationMessage);

            // Initialize the window
            InitializeWindow();
        }

        #region Initialization

        private void InitializeWindow()
        {
            // Set up initial navigation
            NavigateToHome(null, null);

            // Initialize the ViewModel
            _ = Task.Run(async () =>
            {
                await _viewModel.InitializeAsync();
            });
        }

        #endregion

        #region Navigation Event Handlers

        private void NavigateToHome(object sender, RoutedEventArgs e)
        {
            _navigationService.NavigateTo("Home");
            UpdateNavigationSelection("navHome");
        }

        private void NavigateToSettings(object sender, RoutedEventArgs e)
        {
            _navigationService.NavigateTo("Settings");
            UpdateNavigationSelection("navSettings");
        }

        private void NavigateToAbout(object sender, RoutedEventArgs e)
        {
            _navigationService.NavigateTo("About");
            UpdateNavigationSelection("navAbout");
        }

        private void NavigationList_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (NavigationList.SelectedItem is ListBoxItem selectedItem)
            {
                switch (selectedItem.Name)
                {
                    case "navHome":
                        _navigationService.NavigateTo("Home");
                        break;
                    case "navDistributionBoards":
                        _navigationService.NavigateTo("DistributionBoards");
                        break;
                    case "navCircuits":
                        _navigationService.NavigateTo("Circuits");
                        break;
                    case "navResults":
                        _navigationService.NavigateTo("Results");
                        break;
                    case "navSettings":
                        _navigationService.NavigateTo("Settings");
                        break;
                }
            }
        }

        private void UpdateNavigationSelection(string itemName)
        {
            // Update navigation list selection
            foreach (ListBoxItem item in NavigationList.Items)
            {
                item.IsSelected = item.Name == itemName;
            }
        }

        #endregion

        #region Window Event Handlers

        private void HandleNavigationMessage(object recipient, Services.NavigationMessage message)
        {
            // Handle navigation message from ViewModel
            _navigationService.NavigateTo(message.PageName);

            // Update navigation selection based on page
            switch (message.PageName)
            {
                case "Home":
                    UpdateNavigationSelection("navHome");
                    break;
                case "DistributionBoards":
                    UpdateNavigationSelection("navDistributionBoards");
                    break;
                case "Circuits":
                case "CircuitEdit":
                    UpdateNavigationSelection("navCircuits");
                    break;
                case "Results":
                    UpdateNavigationSelection("navResults");
                    break;
                case "Settings":
                    UpdateNavigationSelection("navSettings");
                    break;
            }
        }

        private void Window_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            try
            {
                // Unregister from messages
                WeakReferenceMessenger.Default.Unregister<Services.NavigationMessage>(this);

                // Handle window closing logic
                _viewModel?.OnViewClosing();

                // Close the window service
                WindowService.CloseMainWindow();
            }
            catch (Exception ex)
            {
                // Log error but don't prevent closing
                System.Diagnostics.Debug.WriteLine($"Error during window closing: {ex.Message}");
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Gets the ViewModel for external access
        /// </summary>
        public PowerBIMMainViewModelEnhanced ViewModel => _viewModel;

        /// <summary>
        /// Gets the navigation service
        /// </summary>
        public NavigationService NavigationService => _navigationService;

        #endregion
    }

    /// <summary>
    /// Navigation service for managing page navigation
    /// </summary>
    public class NavigationService
    {
        private readonly Frame _frame;
        private readonly Dictionary<string, Func<Page>> _pageFactories;

        public NavigationService(Frame frame)
        {
            _frame = frame;
            _pageFactories = new Dictionary<string, Func<Page>>();
            RegisterPages();
        }

        private void RegisterPages()
        {
            // Register page factories
            _pageFactories["Home"] = () => new PowerBIMHomePage();
            _pageFactories["DistributionBoards"] = () => new PowerBIMDistributionBoardsPage();
            _pageFactories["Circuits"] = () => new PowerBIMCircuitsPage();
            _pageFactories["CircuitEdit"] = () => new PowerBIMCircuitEditPage();
            _pageFactories["Results"] = () => new PowerBIMResultsPage();
            _pageFactories["Settings"] = () => new PowerBIMSettingsPage();
            _pageFactories["About"] = () => new PowerBIMAboutPage();
        }

        public void NavigateTo(string pageName)
        {
            try
            {
                if (_pageFactories.TryGetValue(pageName, out var pageFactory))
                {
                    var page = pageFactory();
                    _frame.Navigate(page);
                }
                else
                {
                    // Fallback to home page
                    var homePage = new PowerBIMHomePage();
                    _frame.Navigate(homePage);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Navigation error: {ex.Message}");
                // Show error message or fallback page
            }
        }

        public void GoBack()
        {
            if (_frame.CanGoBack)
                _frame.GoBack();
        }

        public void GoForward()
        {
            if (_frame.CanGoForward)
                _frame.GoForward();
        }
    }
}
